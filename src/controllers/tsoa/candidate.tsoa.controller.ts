import { Controller, Get, Post, Put, Delete, Route, Tags, Body, Path, SuccessResponse } from "tsoa";
import { container } from "@/config/inversify.config";
import { CandidateService } from "@/services/candidate.service";
import { CreateCandidateDTO, UpdateCandidateDTO, CandidateResponseDTO } from "@/dto/candidate.dto";
import { TYPES } from "@/config/types";

@Route("candidates")
@Tags("Candidates")
export class CandidateTsoaController extends Controller {
  private candidateService: CandidateService;

  constructor() {
    super();
    this.candidateService = container.get<CandidateService>(TYPES.CandidateService);
  }

  /**
   * Get all candidates
   */
  @Get("/")
  @SuccessResponse("200", "List of candidates")
  public async getCandidates(): Promise<CandidateResponseDTO[]> {
    try {
      const candidates = await this.candidateService.findAll();
      return candidates as CandidateResponseDTO[];
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to fetch candidates");
    }
  }

  /**
   * Get candidate by ID
   */
  @Get("/{id}")
  @SuccessResponse("200", "Candidate details")
  public async getCandidate(@Path() id: number): Promise<CandidateResponseDTO> {
    try {
      const candidate = await this.candidateService.findById(id);
      
      if (!candidate) {
        this.setStatus(404);
        throw new Error("Candidate not found");
      }
      
      return candidate as CandidateResponseDTO;
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to fetch candidate");
    }
  }

  /**
   * Create a new candidate
   */
  @Post("/")
  @SuccessResponse("201", "Candidate created successfully")
  public async createCandidate(@Body() candidateData: CreateCandidateDTO): Promise<CandidateResponseDTO> {
    try {
      const candidate = await this.candidateService.create(candidateData);
      this.setStatus(201);
      return candidate as CandidateResponseDTO;
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to create candidate");
    }
  }

  /**
   * Update candidate
   */
  @Put("/{id}")
  @SuccessResponse("200", "Candidate updated successfully")
  public async updateCandidate(@Path() id: number, @Body() candidateData: UpdateCandidateDTO): Promise<CandidateResponseDTO> {
    try {
      const candidate = await this.candidateService.update(id, candidateData);
      
      if (!candidate) {
        this.setStatus(404);
        throw new Error("Candidate not found");
      }
      
      return candidate as CandidateResponseDTO;
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to update candidate");
    }
  }

  /**
   * Delete candidate
   */
  @Delete("/{id}")
  @SuccessResponse("204", "Candidate deleted successfully")
  public async deleteCandidate(@Path() id: number): Promise<void> {
    try {
      const success = await this.candidateService.delete(id);
      
      if (!success) {
        this.setStatus(404);
        throw new Error("Candidate not found");
      }
      
      this.setStatus(204);
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to delete candidate");
    }
  }

  /**
   * Score a candidate for a specific phase
   */
  @Post("/{candidateId}/score/{phaseId}")
  @SuccessResponse("200", "Candidate scored successfully")
  public async scoreCandidate(@Path() candidateId: number, @Path() phaseId: number): Promise<{ success: boolean; message: string }> {
    try {
      await this.candidateService.scoreCandidate(candidateId, phaseId);
      
      return {
        success: true,
        message: "Candidate scored successfully"
      };
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to score candidate");
    }
  }
}
