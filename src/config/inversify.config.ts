import { Container } from "inversify";
import { TYPES } from "./types";
import { ProjectService } from "@/services/project.service";
import { CandidateService } from "@/services/candidate.service";
import { HRFlowService } from "@/services/hrflow.service";
import { ScoringService } from "@/services/scoring.service";

const container = new Container();

// Bind services
container.bind<ProjectService>(TYPES.ProjectService).to(ProjectService);
container.bind<CandidateService>(TYPES.CandidateService).to(CandidateService);
container.bind<HRFlowService>(TYPES.HRFlowService).to(HRFlowService);
container.bind<ScoringService>(TYPES.ScoringService).to(ScoringService);

export { container };